/*
 * SPDX-FileCopyrightText: 2024 Waveshare ESP32-S3-Touch-LCD-7 Configuration
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

// Display specifications
#define LCD_WIDTH           800
#define LCD_HEIGHT          480
#define LCD_COLOR_BITS      16

// RGB LCD GPIO pins for Waveshare ESP32-S3-Touch-LCD-7
#define LCD_GPIO_VSYNC      3
#define LCD_GPIO_HSYNC      46
#define LCD_GPIO_DE         5
#define LCD_GPIO_PCLK       7
#define LCD_GPIO_DISP       -1  // Controlled by CH422G EXIO2

// RGB data pins
#define LCD_GPIO_DATA0      14  // B3
#define LCD_GPIO_DATA1      38  // B4
#define LCD_GPIO_DATA2      18  // B5
#define LCD_GPIO_DATA3      17  // B6
#define LCD_GPIO_DATA4      10  // B7
#define LCD_GPIO_DATA5      39  // G2
#define LCD_GPIO_DATA6      0   // G3
#define LCD_GPIO_DATA7      45  // G4
#define LCD_GPIO_DATA8      48  // G5
#define LCD_GPIO_DATA9      47  // G6
#define LCD_GPIO_DATA10     21  // G7
#define LCD_GPIO_DATA11     1   // R3
#define LCD_GPIO_DATA12     2   // R4
#define LCD_GPIO_DATA13     42  // R5
#define LCD_GPIO_DATA14     41  // R6
#define LCD_GPIO_DATA15     40  // R7

// Touch controller (GT911) pins
#define TOUCH_GPIO_SDA      8
#define TOUCH_GPIO_SCL      9
#define TOUCH_GPIO_INT      4
#define TOUCH_GPIO_RST      -1  // Controlled by CH422G EXIO1

// I2C addresses
#define CH422G_I2C_ADDR     0x24
#define GT911_I2C_ADDR      0x5D

// CH422G EXIO pins
#define CH422G_EXIO_TP_RST  1   // Touch reset
#define CH422G_EXIO_LCD_BL  2   // LCD backlight
#define CH422G_EXIO_LCD_RST 3   // LCD reset (not used for RGB)
#define CH422G_EXIO_SD_CS   4   // SD card chip select
#define CH422G_EXIO_USB_SEL 5   // USB/CAN selection

// RGB LCD timing parameters
#define LCD_H_RES           800
#define LCD_V_RES           480
#define LCD_HSYNC_FRONT_PORCH   40
#define LCD_HSYNC_BACK_PORCH    40
#define LCD_HSYNC_PULSE_WIDTH   48
#define LCD_VSYNC_FRONT_PORCH   13
#define LCD_VSYNC_BACK_PORCH    29
#define LCD_VSYNC_PULSE_WIDTH   3
#define LCD_PCLK_HZ         (21 * 1000 * 1000)  // 21MHz

// LVGL configuration
#define LVGL_TICK_PERIOD_MS 2
#define LVGL_BUFFER_HEIGHT  100

#ifdef __cplusplus
}
#endif
