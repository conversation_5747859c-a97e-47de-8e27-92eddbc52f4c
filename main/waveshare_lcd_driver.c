/*
 * SPDX-FileCopyrightText: 2024 Waveshare ESP32-S3-Touch-LCD-7 Driver
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "esp_lcd_panel_rgb.h"
#include "esp_lcd_panel_ops.h"
#include "driver/gpio.h"
#include "driver/i2c_master.h"
#include "esp_err.h"
#include "esp_log.h"
// #include "lvgl.h"  // Temporarily disabled
#include "waveshare_lcd_config.h"

static const char *TAG = "WAVESHARE_LCD";

static esp_lcd_panel_handle_t lcd_panel = NULL;
static SemaphoreHandle_t lvgl_mux = NULL;
static i2c_master_bus_handle_t i2c_bus_handle = NULL;
static i2c_master_dev_handle_t ch422g_handle = NULL;

// CH422G I2C functions
static esp_err_t ch422g_write_reg(uint8_t reg, uint8_t data)
{
    uint8_t write_buf[2] = {reg, data};
    return i2c_master_transmit(ch422g_handle, write_buf, sizeof(write_buf), pdMS_TO_TICKS(1000));
}

static esp_err_t ch422g_init(void)
{
    // Configure I2C master bus
    i2c_master_bus_config_t i2c_mst_config = {
        .clk_source = I2C_CLK_SRC_DEFAULT,
        .i2c_port = I2C_NUM_0,
        .scl_io_num = TOUCH_GPIO_SCL,
        .sda_io_num = TOUCH_GPIO_SDA,
        .glitch_ignore_cnt = 7,
        .flags.enable_internal_pullup = true,
    };

    ESP_ERROR_CHECK(i2c_new_master_bus(&i2c_mst_config, &i2c_bus_handle));

    // Add CH422G device to the I2C bus
    i2c_device_config_t dev_cfg = {
        .dev_addr_length = I2C_ADDR_BIT_LEN_7,
        .device_address = CH422G_I2C_ADDR,
        .scl_speed_hz = 400000,
    };

    ESP_ERROR_CHECK(i2c_master_bus_add_device(i2c_bus_handle, &dev_cfg, &ch422g_handle));

    // Initialize CH422G - set all EXIO pins as outputs
    ESP_ERROR_CHECK(ch422g_write_reg(0x24, 0x00)); // Set direction register (0 = output)

    // Set initial states
    uint8_t exio_state = 0;
    exio_state |= (1 << CH422G_EXIO_TP_RST);   // Touch reset high
    exio_state |= (1 << CH422G_EXIO_LCD_BL);   // Backlight on
    exio_state &= ~(1 << CH422G_EXIO_USB_SEL); // USB mode (pull down)

    ESP_ERROR_CHECK(ch422g_write_reg(0x23, exio_state)); // Set output register

    ESP_LOGI(TAG, "CH422G initialized");
    return ESP_OK;
}

// LVGL flush callback - temporarily disabled
/*
static void lvgl_flush_cb(lv_disp_drv_t *drv, const lv_area_t *area, lv_color_t *color_map)
{
    esp_lcd_panel_handle_t panel_handle = (esp_lcd_panel_handle_t) drv->user_data;
    int offsetx1 = area->x1;
    int offsety1 = area->y1;
    int offsetx2 = area->x2;
    int offsety2 = area->y2;

    // Draw bitmap
    esp_lcd_panel_draw_bitmap(panel_handle, offsetx1, offsety1, offsetx2 + 1, offsety2 + 1, color_map);
    lv_disp_flush_ready(drv);
}

// LVGL tick task
static void lvgl_tick_task(void *arg)
{
    while (1) {
        vTaskDelay(pdMS_TO_TICKS(LVGL_TICK_PERIOD_MS));
        lv_tick_inc(LVGL_TICK_PERIOD_MS);
    }
}
*/

esp_err_t waveshare_lcd_init(void)
{
    ESP_LOGI(TAG, "Initialize Waveshare ESP32-S3-Touch-LCD-7");
    
    // Initialize CH422G I/O expander
    ESP_ERROR_CHECK(ch422g_init());
    
    // Create RGB LCD panel
    esp_lcd_rgb_panel_config_t panel_config = {
        .data_width = 16,
        .num_fbs = 1,
        .clk_src = LCD_CLK_SRC_DEFAULT,
        .disp_gpio_num = LCD_GPIO_DISP,
        .pclk_gpio_num = LCD_GPIO_PCLK,
        .vsync_gpio_num = LCD_GPIO_VSYNC,
        .hsync_gpio_num = LCD_GPIO_HSYNC,
        .de_gpio_num = LCD_GPIO_DE,
        .data_gpio_nums = {
            LCD_GPIO_DATA0, LCD_GPIO_DATA1, LCD_GPIO_DATA2, LCD_GPIO_DATA3,
            LCD_GPIO_DATA4, LCD_GPIO_DATA5, LCD_GPIO_DATA6, LCD_GPIO_DATA7,
            LCD_GPIO_DATA8, LCD_GPIO_DATA9, LCD_GPIO_DATA10, LCD_GPIO_DATA11,
            LCD_GPIO_DATA12, LCD_GPIO_DATA13, LCD_GPIO_DATA14, LCD_GPIO_DATA15,
        },
        .timings = {
            .pclk_hz = LCD_PCLK_HZ,
            .h_res = LCD_H_RES,
            .v_res = LCD_V_RES,
            .hsync_front_porch = LCD_HSYNC_FRONT_PORCH,
            .hsync_back_porch = LCD_HSYNC_BACK_PORCH,
            .hsync_pulse_width = LCD_HSYNC_PULSE_WIDTH,
            .vsync_front_porch = LCD_VSYNC_FRONT_PORCH,
            .vsync_back_porch = LCD_VSYNC_BACK_PORCH,
            .vsync_pulse_width = LCD_VSYNC_PULSE_WIDTH,
            .flags.pclk_active_neg = false,
        },
        .flags.fb_in_psram = true,
    };
    
    ESP_ERROR_CHECK(esp_lcd_new_rgb_panel(&panel_config, &lcd_panel));
    ESP_ERROR_CHECK(esp_lcd_panel_reset(lcd_panel));
    ESP_ERROR_CHECK(esp_lcd_panel_init(lcd_panel));
    
    ESP_LOGI(TAG, "RGB LCD panel created");
    return ESP_OK;
}

esp_err_t waveshare_lvgl_init(void)
{
    ESP_LOGI(TAG, "LVGL initialization temporarily disabled");

    // Create mutex for thread safety
    lvgl_mux = xSemaphoreCreateMutex();
    assert(lvgl_mux);

    ESP_LOGI(TAG, "Basic display mutex initialized");
    return ESP_OK;
}

bool waveshare_lvgl_lock(int timeout_ms)
{
    if (lvgl_mux) {
        return xSemaphoreTake(lvgl_mux, pdMS_TO_TICKS(timeout_ms)) == pdTRUE;
    }
    return true;
}

void waveshare_lvgl_unlock(void)
{
    if (lvgl_mux) {
        xSemaphoreGive(lvgl_mux);
    }
}

esp_err_t waveshare_lcd_set_backlight(bool on)
{
    uint8_t exio_state = 0;
    exio_state |= (1 << CH422G_EXIO_TP_RST);   // Touch reset high
    if (on) {
        exio_state |= (1 << CH422G_EXIO_LCD_BL);   // Backlight on
    }
    exio_state &= ~(1 << CH422G_EXIO_USB_SEL); // USB mode

    return ch422g_write_reg(0x23, exio_state);
}

void waveshare_create_red_box(void)
{
    ESP_LOGI(TAG, "Creating red box 240x240 in center of display using direct RGB drawing");

    if (!lcd_panel) {
        ESP_LOGE(TAG, "LCD panel not initialized");
        return;
    }

    // Calculate center position for 240x240 box on 800x480 display
    int box_width = 240;
    int box_height = 240;
    int box_x = (LCD_WIDTH - box_width) / 2;   // (800 - 240) / 2 = 280
    int box_y = (LCD_HEIGHT - box_height) / 2; // (480 - 240) / 2 = 120

    // Create a buffer for the red box (RGB565 format)
    size_t buffer_size = box_width * box_height * 2; // 2 bytes per pixel for RGB565
    uint16_t *red_buffer = heap_caps_malloc(buffer_size, MALLOC_CAP_DMA);
    if (!red_buffer) {
        ESP_LOGE(TAG, "Failed to allocate memory for red box buffer");
        return;
    }

    // Fill buffer with red color (RGB565: 0xF800 = red)
    uint16_t red_color = 0xF800; // Red in RGB565 format
    for (int i = 0; i < box_width * box_height; i++) {
        red_buffer[i] = red_color;
    }

    // Draw the red box to the display
    esp_err_t ret = esp_lcd_panel_draw_bitmap(lcd_panel, box_x, box_y,
                                              box_x + box_width, box_y + box_height,
                                              red_buffer);

    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Red box drawn successfully at position (%d, %d) with size %dx%d",
                 box_x, box_y, box_width, box_height);
    } else {
        ESP_LOGE(TAG, "Failed to draw red box: %s", esp_err_to_name(ret));
    }

    // Free the buffer
    free(red_buffer);
}
