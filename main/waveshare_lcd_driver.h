/*
 * SPDX-FileCopyrightText: 2024 Waveshare ESP32-S3-Touch-LCD-7 Driver
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#pragma once

#include "esp_err.h"
// #include "lvgl.h"  // Temporarily disabled

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize the Waveshare LCD display
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t waveshare_lcd_init(void);

/**
 * @brief Initialize LVGL with the display
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t waveshare_lvgl_init(void);

/**
 * @brief Lock LVGL mutex for thread-safe operations
 * 
 * @param timeout_ms Timeout in milliseconds
 * @return true if lock acquired successfully
 */
bool waveshare_lvgl_lock(int timeout_ms);

/**
 * @brief Unlock LVGL mutex
 */
void waveshare_lvgl_unlock(void);

/**
 * @brief Control LCD backlight
 * 
 * @param on true to turn on, false to turn off
 * @return esp_err_t ESP_OK on success
 */
esp_err_t waveshare_lcd_set_backlight(bool on);

/**
 * @brief Create and display a red box in the center of the screen
 */
void waveshare_create_red_box(void);

#ifdef __cplusplus
}
#endif
