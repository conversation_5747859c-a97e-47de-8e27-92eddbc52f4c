{"idf.openOcdConfigs": ["interface/ftdi/esp_ftdi.cfg", "target/esp32s3.cfg"], "idf.customExtraVars": {"IDF_TARGET": "esp32s3"}, "clangd.path": "/home/<USER>/.espressif/tools/esp-clang/esp-19.1.2_20250312/esp-clang/bin/clangd", "clangd.arguments": ["--background-index", "--query-driver=/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc", "--compile-commands-dir=/home/<USER>/Documents/esp-idf-video-streaming-main/build"], "idf.port": "/dev/ttyACM0", "idf.flashType": "UART"}