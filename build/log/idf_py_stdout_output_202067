Command: cmake -G Ninja -DPYTHON_DEPS_CHECKED=1 -DPYTHON=/home/<USER>/.espressif/python_env/idf6.0_py3.12_env/bin/python -DESP_PLATFORM=1 -DCCACHE_ENABLE=0 /home/<USER>/Documents/esp-idf-video-streaming-main
-- IDF_TARGET is not set, guessed 'esp32s3' from sdkconfig '/home/<USER>/Documents/esp-idf-video-streaming-main/sdkconfig'
-- Found Git: /usr/bin/git (found version "2.43.0")
-- Minimal build - OFF
-- The C compiler identification is GNU 15.1.0
-- The CXX compiler identification is GNU 15.1.0
-- The ASM compiler identification is GNU
-- Found assembler: /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-15.1.0_20250607/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-15.1.0_20250607/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-15.1.0_20250607/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
NOTICE: Manifest files have changed, solving dependencies.
........-- Configuring incomplete, errors occurred!
