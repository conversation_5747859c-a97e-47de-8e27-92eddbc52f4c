Command: cmake -G Ninja -DPYTHON_DEPS_CHECKED=1 -DPYTHON=/home/<USER>/.espressif/python_env/idf6.0_py3.12_env/bin/python -DESP_PLATFORM=1 -DCCACHE_ENABLE=0 /home/<USER>/Documents/esp-idf-video-streaming-main
CMake Error at /home/<USER>/esp/esp-idf/tools/cmake/build.cmake:649 (message):
  WARNING: Component "espressif/esp32_display_panel" not found

  WARNING: Component "espressif/esp32_io_expander" not found

  WARNING: Component "espressif/lvgl" not found

  WARNING: Component "espressif/esp32_display_panel" not found

  WARNING: Component "espressif/esp32_io_expander" not found

  WARNING: Component "espressif/lvgl" not found

  ERROR: Because project depends on espressif/lvgl (^8.4.0) which doesn't
  match any versions, version solving failed.

Call Stack (most recent call first):
  /home/<USER>/esp/esp-idf/tools/cmake/project.cmake:740 (idf_build_process)
  CMakeLists.txt:6 (project)


