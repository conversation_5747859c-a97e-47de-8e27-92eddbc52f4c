Command: ninja all
[1/10] Performing build step for 'bootloader'
[1/1] cd /home/<USER>/Documents/esp-idf-video-streaming-main/build/bootloader && /home/<USER>/.espressif/python_env/idf6.0_py3.12_env/bin/python /home/<USER>/esp/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /home/<USER>/Documents/esp-idf-video-streaming-main/build/bootloader/bootloader.bin
Bootloader binary size 0x5320 bytes. 0x2ce0 bytes (35%) free.
[2/10] No install step for 'bootloader'
[3/10] Completed 'bootloader'
[4/10] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj
[5/10] Linking C static library esp-idf/main/libmain.a
[6/10] Generating esp-idf/esp_system/ld/sections.ld
info: INFO: Symbol FRAME_RATE defined in multiple locations (see below). Please check if this is a correct behavior or a random name match:
    /home/<USER>/Documents/esp-idf-video-streaming-main/main/Kconfig.projbuild:82
    /home/<USER>/Documents/esp-idf-video-streaming-main/main/Kconfig.projbuild:73
[7/10] Building C object CMakeFiles/video-streaming.elf.dir/project_elf_src_esp32s3.c.obj
[8/10] Linking CXX executable video-streaming.elf
[9/10] Generating binary image from built executable
esptool.py v4.10.dev2
Creating esp32s3 image...
Merged 2 ELF sections
Successfully created esp32s3 image.
Generated /home/<USER>/Documents/esp-idf-video-streaming-main/build/video-streaming.bin
[10/10] cd /home/<USER>/Documents/esp-idf-video-streaming-main/build && /home/<USER>/.espressif/python_env/idf6.0_py3.12_env/bin/python /home/<USER>/esp/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app /home/<USER>/Documents/esp-idf-video-streaming-main/build/partition_table/partition-table.bin /home/<USER>/Documents/esp-idf-video-streaming-main/build/video-streaming.bin
video-streaming.bin binary size 0xca3c0 bytes. Smallest app partition is 0x100000 bytes. 0x35c40 bytes (21%) free.
