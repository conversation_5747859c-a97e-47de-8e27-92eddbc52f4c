Command: ninja all
[0/1] Re-running CMake...
-- Minimal build - OFF
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
NOTICE: Manifest files have changed, solving dependencies.
..NOTICE: Updating lock file at /home/<USER>/Documents/esp-idf-video-streaming-main/dependencies.lock
NOTICE: Processing 2 dependencies:
NOTICE: [1/2] espressif/mdns (1.8.2)
NOTICE: [2/2] idf (6.0.0)
-- ESP-TEE is currently supported only on the esp32c6;esp32h2;esp32c5 SoCs
-- Project sdkconfig file /home/<USER>/Documents/esp-idf-video-streaming-main/sdkconfig
Loading defaults file /home/<USER>/Documents/esp-idf-video-streaming-main/sdkconfig.defaults...
/home/<USER>/Documents/esp-idf-video-streaming-main/sdkconfig.defaults:18 CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM was replaced with CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM 
/home/<USER>/Documents/esp-idf-video-streaming-main/sdkconfig.defaults:19 CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM was replaced with CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM 
/home/<USER>/Documents/esp-idf-video-streaming-main/sdkconfig.defaults:20 CONFIG_ESP32_WIFI_STATIC_TX_BUFFER_NUM was replaced with CONFIG_ESP_WIFI_STATIC_TX_BUFFER_NUM 
/home/<USER>/Documents/esp-idf-video-streaming-main/sdkconfig.defaults:21 CONFIG_ESP32_WIFI_CACHE_TX_BUFFER_NUM was replaced with CONFIG_ESP_WIFI_CACHE_TX_BUFFER_NUM 
/home/<USER>/Documents/esp-idf-video-streaming-main/sdkconfig.defaults:22 CONFIG_ESP32_WIFI_RX_BA_WIN was replaced with CONFIG_ESP_WIFI_RX_BA_WIN 
/home/<USER>/Documents/esp-idf-video-streaming-main/sdkconfig.defaults:38 CONFIG_ESP32S3_DEFAULT_CPU_FREQ_240 was replaced with CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240 
/home/<USER>/Documents/esp-idf-video-streaming-main/sdkconfig.defaults:39 CONFIG_ESP32S3_DEFAULT_CPU_FREQ_MHZ was replaced with CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ 
warning: unknown kconfig symbol 'ESP32S2_DEFAULT_CPU_FREQ_240' assigned to 'y' in /home/<USER>/Documents/esp-idf-video-streaming-main/sdkconfig.defaults
warning: unknown kconfig symbol 'ESP32S2_DEFAULT_CPU_FREQ_MHZ' assigned to '240' in /home/<USER>/Documents/esp-idf-video-streaming-main/sdkconfig.defaults
-- Compiler supported targets: xtensa-esp-elf
-- App "video-streaming" version: 1
-- USING O3
-- Adding linker script /home/<USER>/Documents/esp-idf-video-streaming-main/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script /home/<USER>/Documents/esp-idf-video-streaming-main/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_master.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_50.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_smp.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_dtm.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_test.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_scan.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libc.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script /home/<USER>/esp/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console conversions cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_bitscrambler esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_i3c esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sd_intf esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_twai esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump espressif__mdns esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread rt sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: /home/<USER>/esp/esp-idf/components/app_trace /home/<USER>/esp/esp-idf/components/app_update /home/<USER>/esp/esp-idf/components/bootloader /home/<USER>/esp/esp-idf/components/bootloader_support /home/<USER>/esp/esp-idf/components/bt /home/<USER>/esp/esp-idf/components/cmock /home/<USER>/esp/esp-idf/components/console /home/<USER>/Documents/esp-idf-video-streaming-main/components/conversions /home/<USER>/esp/esp-idf/components/cxx /home/<USER>/esp/esp-idf/components/driver /home/<USER>/esp/esp-idf/components/efuse /home/<USER>/esp/esp-idf/components/esp-tls /home/<USER>/esp/esp-idf/components/esp_adc /home/<USER>/esp/esp-idf/components/esp_app_format /home/<USER>/esp/esp-idf/components/esp_bootloader_format /home/<USER>/esp/esp-idf/components/esp_coex /home/<USER>/esp/esp-idf/components/esp_common /home/<USER>/esp/esp-idf/components/esp_driver_ana_cmpr /home/<USER>/esp/esp-idf/components/esp_driver_bitscrambler /home/<USER>/esp/esp-idf/components/esp_driver_cam /home/<USER>/esp/esp-idf/components/esp_driver_dac /home/<USER>/esp/esp-idf/components/esp_driver_gpio /home/<USER>/esp/esp-idf/components/esp_driver_gptimer /home/<USER>/esp/esp-idf/components/esp_driver_i2c /home/<USER>/esp/esp-idf/components/esp_driver_i2s /home/<USER>/esp/esp-idf/components/esp_driver_i3c /home/<USER>/esp/esp-idf/components/esp_driver_isp /home/<USER>/esp/esp-idf/components/esp_driver_jpeg /home/<USER>/esp/esp-idf/components/esp_driver_ledc /home/<USER>/esp/esp-idf/components/esp_driver_mcpwm /home/<USER>/esp/esp-idf/components/esp_driver_parlio /home/<USER>/esp/esp-idf/components/esp_driver_pcnt /home/<USER>/esp/esp-idf/components/esp_driver_ppa /home/<USER>/esp/esp-idf/components/esp_driver_rmt /home/<USER>/esp/esp-idf/components/esp_driver_sd_intf /home/<USER>/esp/esp-idf/components/esp_driver_sdio /home/<USER>/esp/esp-idf/components/esp_driver_sdm /home/<USER>/esp/esp-idf/components/esp_driver_sdmmc /home/<USER>/esp/esp-idf/components/esp_driver_sdspi /home/<USER>/esp/esp-idf/components/esp_driver_spi /home/<USER>/esp/esp-idf/components/esp_driver_touch_sens /home/<USER>/esp/esp-idf/components/esp_driver_tsens /home/<USER>/esp/esp-idf/components/esp_driver_twai /home/<USER>/esp/esp-idf/components/esp_driver_uart /home/<USER>/esp/esp-idf/components/esp_driver_usb_serial_jtag /home/<USER>/esp/esp-idf/components/esp_eth /home/<USER>/esp/esp-idf/components/esp_event /home/<USER>/esp/esp-idf/components/esp_gdbstub /home/<USER>/esp/esp-idf/components/esp_hid /home/<USER>/esp/esp-idf/components/esp_http_client /home/<USER>/esp/esp-idf/components/esp_http_server /home/<USER>/esp/esp-idf/components/esp_https_ota /home/<USER>/esp/esp-idf/components/esp_https_server /home/<USER>/esp/esp-idf/components/esp_hw_support /home/<USER>/esp/esp-idf/components/esp_lcd /home/<USER>/esp/esp-idf/components/esp_local_ctrl /home/<USER>/esp/esp-idf/components/esp_mm /home/<USER>/esp/esp-idf/components/esp_netif /home/<USER>/esp/esp-idf/components/esp_netif_stack /home/<USER>/esp/esp-idf/components/esp_partition /home/<USER>/esp/esp-idf/components/esp_phy /home/<USER>/esp/esp-idf/components/esp_pm /home/<USER>/esp/esp-idf/components/esp_psram /home/<USER>/esp/esp-idf/components/esp_ringbuf /home/<USER>/esp/esp-idf/components/esp_rom /home/<USER>/esp/esp-idf/components/esp_security /home/<USER>/esp/esp-idf/components/esp_system /home/<USER>/esp/esp-idf/components/esp_timer /home/<USER>/esp/esp-idf/components/esp_vfs_console /home/<USER>/esp/esp-idf/components/esp_wifi /home/<USER>/esp/esp-idf/components/espcoredump /home/<USER>/Documents/esp-idf-video-streaming-main/managed_components/espressif__mdns /home/<USER>/esp/esp-idf/components/esptool_py /home/<USER>/esp/esp-idf/components/fatfs /home/<USER>/esp/esp-idf/components/freertos /home/<USER>/esp/esp-idf/components/hal /home/<USER>/esp/esp-idf/components/heap /home/<USER>/esp/esp-idf/components/http_parser /home/<USER>/esp/esp-idf/components/idf_test /home/<USER>/esp/esp-idf/components/ieee802154 /home/<USER>/esp/esp-idf/components/json /home/<USER>/esp/esp-idf/components/log /home/<USER>/esp/esp-idf/components/lwip /home/<USER>/Documents/esp-idf-video-streaming-main/main /home/<USER>/esp/esp-idf/components/mbedtls /home/<USER>/esp/esp-idf/components/mqtt /home/<USER>/esp/esp-idf/components/newlib /home/<USER>/esp/esp-idf/components/nvs_flash /home/<USER>/esp/esp-idf/components/nvs_sec_provider /home/<USER>/esp/esp-idf/components/openthread /home/<USER>/esp/esp-idf/components/partition_table /home/<USER>/esp/esp-idf/components/perfmon /home/<USER>/esp/esp-idf/components/protobuf-c /home/<USER>/esp/esp-idf/components/protocomm /home/<USER>/esp/esp-idf/components/pthread /home/<USER>/esp/esp-idf/components/rt /home/<USER>/esp/esp-idf/components/sdmmc /home/<USER>/esp/esp-idf/components/soc /home/<USER>/esp/esp-idf/components/spi_flash /home/<USER>/esp/esp-idf/components/spiffs /home/<USER>/esp/esp-idf/components/tcp_transport /home/<USER>/esp/esp-idf/components/touch_element /home/<USER>/esp/esp-idf/components/ulp /home/<USER>/esp/esp-idf/components/unity /home/<USER>/esp/esp-idf/components/usb /home/<USER>/esp/esp-idf/components/vfs /home/<USER>/esp/esp-idf/components/wear_levelling /home/<USER>/esp/esp-idf/components/wifi_provisioning /home/<USER>/esp/esp-idf/components/wpa_supplicant /home/<USER>/esp/esp-idf/components/xtensa
-- Configuring done (5.9s)
-- Generating done (1.0s)
-- Build files have been written to: /home/<USER>/Documents/esp-idf-video-streaming-main/build
[1/22] Linking C static library esp-idf/nvs_sec_provider/libnvs_sec_provider.a
[2/22] Linking C static library esp-idf/perfmon/libperfmon.a
[3/22] Linking C static library esp-idf/rt/librt.a
[4/22] Linking C static library esp-idf/touch_element/libtouch_element.a
[5/22] Linking C static library esp-idf/spiffs/libspiffs.a
[6/22] Linking C static library esp-idf/fatfs/libfatfs.a
[7/22] Linking C static library esp-idf/mqtt/libmqtt.a
[8/22] Linking C static library esp-idf/wifi_provisioning/libwifi_provisioning.a
[9/22] Linking C static library esp-idf/conversions/libconversions.a
[10/22] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/http_server.c.obj
[11/22] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/waveshare_lcd_driver.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/waveshare_lcd_driver.c.obj 
/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-15.1.0_20250607/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DESP_MDNS_VERSION_NUMBER=\"1.8.2\" -DESP_PLATFORM -DIDF_VER=\"v6.0-dev-1833-g758939caec\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -DUNITY_INCLUDE_CONFIG_H -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -I/home/<USER>/Documents/esp-idf-video-streaming-main/build/config -I/home/<USER>/Documents/esp-idf-video-streaming-main/main -I/home/<USER>/esp/esp-idf/components/newlib/platform_include -I/home/<USER>/esp/esp-idf/components/freertos/config/include -I/home/<USER>/esp/esp-idf/components/freertos/config/include/freertos -I/home/<USER>/esp/esp-idf/components/freertos/config/xtensa/include -I/home/<USER>/esp/esp-idf/components/freertos/FreeRTOS-Kernel/include -I/home/<USER>/esp/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -I/home/<USER>/esp/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -I/home/<USER>/esp/esp-idf/components/freertos/esp_additions/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/power_supply/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/mspi_timing_tuning/port/esp32s3/. -I/home/<USER>/esp/esp-idf/components/esp_hw_support/mspi_timing_tuning/port/esp32s3/include -I/home/<USER>/esp/esp-idf/components/heap/include -I/home/<USER>/esp/esp-idf/components/heap/tlsf -I/home/<USER>/esp/esp-idf/components/log/include -I/home/<USER>/esp/esp-idf/components/soc/include -I/home/<USER>/esp/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp/esp-idf/components/hal/include -I/home/<USER>/esp/esp-idf/components/esp_rom/include -I/home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp/esp-idf/components/esp_common/include -I/home/<USER>/esp/esp-idf/components/esp_system/include -I/home/<USER>/esp/esp-idf/components/esp_system/port/soc -I/home/<USER>/esp/esp-idf/components/esp_system/port/include/private -I/home/<USER>/esp/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp/esp-idf/components/xtensa/include -I/home/<USER>/esp/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp/esp-idf/components/lwip/include -I/home/<USER>/esp/esp-idf/components/lwip/include/apps -I/home/<USER>/esp/esp-idf/components/lwip/include/apps/sntp -I/home/<USER>/esp/esp-idf/components/lwip/lwip/src/include -I/home/<USER>/esp/esp-idf/components/lwip/port/include -I/home/<USER>/esp/esp-idf/components/lwip/port/freertos/include -I/home/<USER>/esp/esp-idf/components/lwip/port/esp32xx/include -I/home/<USER>/esp/esp-idf/components/lwip/port/esp32xx/include/arch -I/home/<USER>/esp/esp-idf/components/lwip/port/esp32xx/include/sys -I/home/<USER>/Documents/esp-idf-video-streaming-main/managed_components/espressif__mdns/include -I/home/<USER>/esp/esp-idf/components/console -I/home/<USER>/esp/esp-idf/components/vfs/include -I/home/<USER>/esp/esp-idf/components/esp_vfs_console/include -I/home/<USER>/esp/esp-idf/components/esp_netif/include -I/home/<USER>/esp/esp-idf/components/esp_event/include -I/home/<USER>/esp/esp-idf/components/esp_app_format/include -I/home/<USER>/esp/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp/esp-idf/components/app_update/include -I/home/<USER>/esp/esp-idf/components/bootloader_support/include -I/home/<USER>/esp/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp/esp-idf/components/esp_partition/include -I/home/<USER>/esp/esp-idf/components/efuse/include -I/home/<USER>/esp/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_security/include -I/home/<USER>/esp/esp-idf/components/esp_driver_gpio/include -I/home/<USER>/esp/esp-idf/components/esp_pm/include -I/home/<USER>/esp/esp-idf/components/mbedtls/port/include -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/include -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/library -I/home/<USER>/esp/esp-idf/components/mbedtls/esp_crt_bundle/include -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/3rdparty/everest/include -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/3rdparty/p256-m -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -I/home/<USER>/esp/esp-idf/components/esp_mm/include -I/home/<USER>/esp/esp-idf/components/spi_flash/include -I/home/<USER>/esp/esp-idf/components/pthread/include -I/home/<USER>/esp/esp-idf/components/esp_timer/include -I/home/<USER>/esp/esp-idf/components/esp_driver_gptimer/include -I/home/<USER>/esp/esp-idf/components/esp_ringbuf/include -I/home/<USER>/esp/esp-idf/components/esp_driver_uart/include -I/home/<USER>/esp/esp-idf/components/app_trace/include -I/home/<USER>/esp/esp-idf/components/nvs_flash/include -I/home/<USER>/esp/esp-idf/components/esp_phy/include -I/home/<USER>/esp/esp-idf/components/esp_phy/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_driver_usb_serial_jtag/include -I/home/<USER>/esp/esp-idf/components/wpa_supplicant/include -I/home/<USER>/esp/esp-idf/components/wpa_supplicant/port/include -I/home/<USER>/esp/esp-idf/components/wpa_supplicant/esp_supplicant/include -I/home/<USER>/esp/esp-idf/components/esp_coex/include -I/home/<USER>/esp/esp-idf/components/esp_wifi/include -I/home/<USER>/esp/esp-idf/components/esp_wifi/include/local -I/home/<USER>/esp/esp-idf/components/esp_wifi/wifi_apps/include -I/home/<USER>/esp/esp-idf/components/esp_wifi/wifi_apps/nan_app/include -I/home/<USER>/esp/esp-idf/components/esp_driver_spi/include -I/home/<USER>/esp/esp-idf/components/esp_gdbstub/include -I/home/<USER>/esp/esp-idf/components/unity/include -I/home/<USER>/esp/esp-idf/components/unity/unity/src -I/home/<USER>/esp/esp-idf/components/cmock/CMock/src -I/home/<USER>/esp/esp-idf/components/esp_driver_pcnt/include -I/home/<USER>/esp/esp-idf/components/esp_driver_mcpwm/include -I/home/<USER>/esp/esp-idf/components/esp_driver_ana_cmpr/include -I/home/<USER>/esp/esp-idf/components/esp_driver_i2s/include -I/home/<USER>/esp/esp-idf/components/sdmmc/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sd_intf/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdmmc/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdmmc/legacy/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdspi/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdio/include -I/home/<USER>/esp/esp-idf/components/esp_driver_dac/include -I/home/<USER>/esp/esp-idf/components/esp_driver_bitscrambler/include -I/home/<USER>/esp/esp-idf/components/esp_driver_rmt/include -I/home/<USER>/esp/esp-idf/components/esp_driver_tsens/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdm/include -I/home/<USER>/esp/esp-idf/components/esp_driver_i2c/include -I/home/<USER>/esp/esp-idf/components/esp_driver_ledc/include -I/home/<USER>/esp/esp-idf/components/esp_driver_parlio/include -I/home/<USER>/esp/esp-idf/components/esp_driver_twai/include -I/home/<USER>/esp/esp-idf/components/driver/deprecated -I/home/<USER>/esp/esp-idf/components/driver/i2c/include -I/home/<USER>/esp/esp-idf/components/driver/touch_sensor/include -I/home/<USER>/esp/esp-idf/components/driver/twai/include -I/home/<USER>/esp/esp-idf/components/driver/touch_sensor/esp32s3/include -I/home/<USER>/esp/esp-idf/components/http_parser -I/home/<USER>/esp/esp-idf/components/esp-tls -I/home/<USER>/esp/esp-idf/components/esp-tls/esp-tls-crypto -I/home/<USER>/esp/esp-idf/components/esp_adc/include -I/home/<USER>/esp/esp-idf/components/esp_adc/interface -I/home/<USER>/esp/esp-idf/components/esp_adc/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_driver_isp/include -I/home/<USER>/esp/esp-idf/components/esp_driver_cam/include -I/home/<USER>/esp/esp-idf/components/esp_driver_cam/interface -I/home/<USER>/esp/esp-idf/components/esp_driver_cam/dvp/include -I/home/<USER>/esp/esp-idf/components/esp_driver_i3c/include -I/home/<USER>/esp/esp-idf/components/esp_psram/include -I/home/<USER>/esp/esp-idf/components/esp_psram/xip_impl/include -I/home/<USER>/esp/esp-idf/components/esp_driver_jpeg/include -I/home/<USER>/esp/esp-idf/components/esp_driver_ppa/include -I/home/<USER>/esp/esp-idf/components/esp_driver_touch_sens/include -I/home/<USER>/esp/esp-idf/components/esp_driver_touch_sens/hw_ver2/include -I/home/<USER>/esp/esp-idf/components/esp_eth/include -I/home/<USER>/esp/esp-idf/components/esp_hid/include -I/home/<USER>/esp/esp-idf/components/tcp_transport/include -I/home/<USER>/esp/esp-idf/components/esp_http_client/include -I/home/<USER>/esp/esp-idf/components/esp_http_server/include -I/home/<USER>/esp/esp-idf/components/esp_https_ota/include -I/home/<USER>/esp/esp-idf/components/esp_https_server/include -I/home/<USER>/esp/esp-idf/components/esp_lcd/include -I/home/<USER>/esp/esp-idf/components/esp_lcd/interface -I/home/<USER>/esp/esp-idf/components/esp_lcd/rgb/include -I/home/<USER>/esp/esp-idf/components/protobuf-c/protobuf-c -I/home/<USER>/esp/esp-idf/components/protocomm/include/common -I/home/<USER>/esp/esp-idf/components/protocomm/include/security -I/home/<USER>/esp/esp-idf/components/protocomm/include/transports -I/home/<USER>/esp/esp-idf/components/protocomm/include/crypto/srp6a -I/home/<USER>/esp/esp-idf/components/protocomm/proto-c -I/home/<USER>/esp/esp-idf/components/esp_local_ctrl/include -I/home/<USER>/esp/esp-idf/components/espcoredump/include -I/home/<USER>/esp/esp-idf/components/espcoredump/include/port/xtensa -I/home/<USER>/esp/esp-idf/components/wear_levelling/include -I/home/<USER>/esp/esp-idf/components/fatfs/diskio -I/home/<USER>/esp/esp-idf/components/fatfs/src -I/home/<USER>/esp/esp-idf/components/fatfs/vfs -I/home/<USER>/esp/esp-idf/components/idf_test/include -I/home/<USER>/esp/esp-idf/components/idf_test/include/esp32s3 -I/home/<USER>/esp/esp-idf/components/ieee802154/include -I/home/<USER>/esp/esp-idf/components/json/cJSON -I/home/<USER>/esp/esp-idf/components/mqtt/esp-mqtt/include -I/home/<USER>/esp/esp-idf/components/nvs_sec_provider/include -I/home/<USER>/esp/esp-idf/components/perfmon/include -I/home/<USER>/esp/esp-idf/components/rt/include -I/home/<USER>/esp/esp-idf/components/spiffs/include -I/home/<USER>/esp/esp-idf/components/touch_element/include -I/home/<USER>/esp/esp-idf/components/usb/include -I/home/<USER>/esp/esp-idf/components/wifi_provisioning/include -I/home/<USER>/Documents/esp-idf-video-streaming-main/components/conversions/include -I/home/<USER>/Documents/esp-idf-video-streaming-main/components/conversions/private_include -mlongcalls -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -mdisable-hardware-atomics -Og -fno-shrink-wrap -fmacro-prefix-map=/home/<USER>/Documents/esp-idf-video-streaming-main=. -fmacro-prefix-map=/home/<USER>/esp/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -fzero-init-padding-bits=all -fno-malloc-dce -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/waveshare_lcd_driver.c.obj -MF esp-idf/main/CMakeFiles/__idf_main.dir/waveshare_lcd_driver.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/waveshare_lcd_driver.c.obj -c /home/<USER>/Documents/esp-idf-video-streaming-main/main/waveshare_lcd_driver.c
/home/<USER>/Documents/esp-idf-video-streaming-main/main/waveshare_lcd_driver.c: In function 'waveshare_lcd_init':
/home/<USER>/Documents/esp-idf-video-streaming-main/main/waveshare_lcd_driver.c:108:10: error: 'esp_lcd_rgb_panel_config_t' has no member named 'psram_trans_align'
  108 |         .psram_trans_align = 64,
      |          ^~~~~~~~~~~~~~~~~
[12/22] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj 
/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-15.1.0_20250607/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DESP_MDNS_VERSION_NUMBER=\"1.8.2\" -DESP_PLATFORM -DIDF_VER=\"v6.0-dev-1833-g758939caec\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -DUNITY_INCLUDE_CONFIG_H -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -I/home/<USER>/Documents/esp-idf-video-streaming-main/build/config -I/home/<USER>/Documents/esp-idf-video-streaming-main/main -I/home/<USER>/esp/esp-idf/components/newlib/platform_include -I/home/<USER>/esp/esp-idf/components/freertos/config/include -I/home/<USER>/esp/esp-idf/components/freertos/config/include/freertos -I/home/<USER>/esp/esp-idf/components/freertos/config/xtensa/include -I/home/<USER>/esp/esp-idf/components/freertos/FreeRTOS-Kernel/include -I/home/<USER>/esp/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -I/home/<USER>/esp/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -I/home/<USER>/esp/esp-idf/components/freertos/esp_additions/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/power_supply/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/mspi_timing_tuning/port/esp32s3/. -I/home/<USER>/esp/esp-idf/components/esp_hw_support/mspi_timing_tuning/port/esp32s3/include -I/home/<USER>/esp/esp-idf/components/heap/include -I/home/<USER>/esp/esp-idf/components/heap/tlsf -I/home/<USER>/esp/esp-idf/components/log/include -I/home/<USER>/esp/esp-idf/components/soc/include -I/home/<USER>/esp/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp/esp-idf/components/hal/include -I/home/<USER>/esp/esp-idf/components/esp_rom/include -I/home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp/esp-idf/components/esp_common/include -I/home/<USER>/esp/esp-idf/components/esp_system/include -I/home/<USER>/esp/esp-idf/components/esp_system/port/soc -I/home/<USER>/esp/esp-idf/components/esp_system/port/include/private -I/home/<USER>/esp/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp/esp-idf/components/xtensa/include -I/home/<USER>/esp/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp/esp-idf/components/lwip/include -I/home/<USER>/esp/esp-idf/components/lwip/include/apps -I/home/<USER>/esp/esp-idf/components/lwip/include/apps/sntp -I/home/<USER>/esp/esp-idf/components/lwip/lwip/src/include -I/home/<USER>/esp/esp-idf/components/lwip/port/include -I/home/<USER>/esp/esp-idf/components/lwip/port/freertos/include -I/home/<USER>/esp/esp-idf/components/lwip/port/esp32xx/include -I/home/<USER>/esp/esp-idf/components/lwip/port/esp32xx/include/arch -I/home/<USER>/esp/esp-idf/components/lwip/port/esp32xx/include/sys -I/home/<USER>/Documents/esp-idf-video-streaming-main/managed_components/espressif__mdns/include -I/home/<USER>/esp/esp-idf/components/console -I/home/<USER>/esp/esp-idf/components/vfs/include -I/home/<USER>/esp/esp-idf/components/esp_vfs_console/include -I/home/<USER>/esp/esp-idf/components/esp_netif/include -I/home/<USER>/esp/esp-idf/components/esp_event/include -I/home/<USER>/esp/esp-idf/components/esp_app_format/include -I/home/<USER>/esp/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp/esp-idf/components/app_update/include -I/home/<USER>/esp/esp-idf/components/bootloader_support/include -I/home/<USER>/esp/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp/esp-idf/components/esp_partition/include -I/home/<USER>/esp/esp-idf/components/efuse/include -I/home/<USER>/esp/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_security/include -I/home/<USER>/esp/esp-idf/components/esp_driver_gpio/include -I/home/<USER>/esp/esp-idf/components/esp_pm/include -I/home/<USER>/esp/esp-idf/components/mbedtls/port/include -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/include -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/library -I/home/<USER>/esp/esp-idf/components/mbedtls/esp_crt_bundle/include -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/3rdparty/everest/include -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/3rdparty/p256-m -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -I/home/<USER>/esp/esp-idf/components/esp_mm/include -I/home/<USER>/esp/esp-idf/components/spi_flash/include -I/home/<USER>/esp/esp-idf/components/pthread/include -I/home/<USER>/esp/esp-idf/components/esp_timer/include -I/home/<USER>/esp/esp-idf/components/esp_driver_gptimer/include -I/home/<USER>/esp/esp-idf/components/esp_ringbuf/include -I/home/<USER>/esp/esp-idf/components/esp_driver_uart/include -I/home/<USER>/esp/esp-idf/components/app_trace/include -I/home/<USER>/esp/esp-idf/components/nvs_flash/include -I/home/<USER>/esp/esp-idf/components/esp_phy/include -I/home/<USER>/esp/esp-idf/components/esp_phy/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_driver_usb_serial_jtag/include -I/home/<USER>/esp/esp-idf/components/wpa_supplicant/include -I/home/<USER>/esp/esp-idf/components/wpa_supplicant/port/include -I/home/<USER>/esp/esp-idf/components/wpa_supplicant/esp_supplicant/include -I/home/<USER>/esp/esp-idf/components/esp_coex/include -I/home/<USER>/esp/esp-idf/components/esp_wifi/include -I/home/<USER>/esp/esp-idf/components/esp_wifi/include/local -I/home/<USER>/esp/esp-idf/components/esp_wifi/wifi_apps/include -I/home/<USER>/esp/esp-idf/components/esp_wifi/wifi_apps/nan_app/include -I/home/<USER>/esp/esp-idf/components/esp_driver_spi/include -I/home/<USER>/esp/esp-idf/components/esp_gdbstub/include -I/home/<USER>/esp/esp-idf/components/unity/include -I/home/<USER>/esp/esp-idf/components/unity/unity/src -I/home/<USER>/esp/esp-idf/components/cmock/CMock/src -I/home/<USER>/esp/esp-idf/components/esp_driver_pcnt/include -I/home/<USER>/esp/esp-idf/components/esp_driver_mcpwm/include -I/home/<USER>/esp/esp-idf/components/esp_driver_ana_cmpr/include -I/home/<USER>/esp/esp-idf/components/esp_driver_i2s/include -I/home/<USER>/esp/esp-idf/components/sdmmc/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sd_intf/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdmmc/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdmmc/legacy/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdspi/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdio/include -I/home/<USER>/esp/esp-idf/components/esp_driver_dac/include -I/home/<USER>/esp/esp-idf/components/esp_driver_bitscrambler/include -I/home/<USER>/esp/esp-idf/components/esp_driver_rmt/include -I/home/<USER>/esp/esp-idf/components/esp_driver_tsens/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdm/include -I/home/<USER>/esp/esp-idf/components/esp_driver_i2c/include -I/home/<USER>/esp/esp-idf/components/esp_driver_ledc/include -I/home/<USER>/esp/esp-idf/components/esp_driver_parlio/include -I/home/<USER>/esp/esp-idf/components/esp_driver_twai/include -I/home/<USER>/esp/esp-idf/components/driver/deprecated -I/home/<USER>/esp/esp-idf/components/driver/i2c/include -I/home/<USER>/esp/esp-idf/components/driver/touch_sensor/include -I/home/<USER>/esp/esp-idf/components/driver/twai/include -I/home/<USER>/esp/esp-idf/components/driver/touch_sensor/esp32s3/include -I/home/<USER>/esp/esp-idf/components/http_parser -I/home/<USER>/esp/esp-idf/components/esp-tls -I/home/<USER>/esp/esp-idf/components/esp-tls/esp-tls-crypto -I/home/<USER>/esp/esp-idf/components/esp_adc/include -I/home/<USER>/esp/esp-idf/components/esp_adc/interface -I/home/<USER>/esp/esp-idf/components/esp_adc/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_driver_isp/include -I/home/<USER>/esp/esp-idf/components/esp_driver_cam/include -I/home/<USER>/esp/esp-idf/components/esp_driver_cam/interface -I/home/<USER>/esp/esp-idf/components/esp_driver_cam/dvp/include -I/home/<USER>/esp/esp-idf/components/esp_driver_i3c/include -I/home/<USER>/esp/esp-idf/components/esp_psram/include -I/home/<USER>/esp/esp-idf/components/esp_psram/xip_impl/include -I/home/<USER>/esp/esp-idf/components/esp_driver_jpeg/include -I/home/<USER>/esp/esp-idf/components/esp_driver_ppa/include -I/home/<USER>/esp/esp-idf/components/esp_driver_touch_sens/include -I/home/<USER>/esp/esp-idf/components/esp_driver_touch_sens/hw_ver2/include -I/home/<USER>/esp/esp-idf/components/esp_eth/include -I/home/<USER>/esp/esp-idf/components/esp_hid/include -I/home/<USER>/esp/esp-idf/components/tcp_transport/include -I/home/<USER>/esp/esp-idf/components/esp_http_client/include -I/home/<USER>/esp/esp-idf/components/esp_http_server/include -I/home/<USER>/esp/esp-idf/components/esp_https_ota/include -I/home/<USER>/esp/esp-idf/components/esp_https_server/include -I/home/<USER>/esp/esp-idf/components/esp_lcd/include -I/home/<USER>/esp/esp-idf/components/esp_lcd/interface -I/home/<USER>/esp/esp-idf/components/esp_lcd/rgb/include -I/home/<USER>/esp/esp-idf/components/protobuf-c/protobuf-c -I/home/<USER>/esp/esp-idf/components/protocomm/include/common -I/home/<USER>/esp/esp-idf/components/protocomm/include/security -I/home/<USER>/esp/esp-idf/components/protocomm/include/transports -I/home/<USER>/esp/esp-idf/components/protocomm/include/crypto/srp6a -I/home/<USER>/esp/esp-idf/components/protocomm/proto-c -I/home/<USER>/esp/esp-idf/components/esp_local_ctrl/include -I/home/<USER>/esp/esp-idf/components/espcoredump/include -I/home/<USER>/esp/esp-idf/components/espcoredump/include/port/xtensa -I/home/<USER>/esp/esp-idf/components/wear_levelling/include -I/home/<USER>/esp/esp-idf/components/fatfs/diskio -I/home/<USER>/esp/esp-idf/components/fatfs/src -I/home/<USER>/esp/esp-idf/components/fatfs/vfs -I/home/<USER>/esp/esp-idf/components/idf_test/include -I/home/<USER>/esp/esp-idf/components/idf_test/include/esp32s3 -I/home/<USER>/esp/esp-idf/components/ieee802154/include -I/home/<USER>/esp/esp-idf/components/json/cJSON -I/home/<USER>/esp/esp-idf/components/mqtt/esp-mqtt/include -I/home/<USER>/esp/esp-idf/components/nvs_sec_provider/include -I/home/<USER>/esp/esp-idf/components/perfmon/include -I/home/<USER>/esp/esp-idf/components/rt/include -I/home/<USER>/esp/esp-idf/components/spiffs/include -I/home/<USER>/esp/esp-idf/components/touch_element/include -I/home/<USER>/esp/esp-idf/components/usb/include -I/home/<USER>/esp/esp-idf/components/wifi_provisioning/include -I/home/<USER>/Documents/esp-idf-video-streaming-main/components/conversions/include -I/home/<USER>/Documents/esp-idf-video-streaming-main/components/conversions/private_include -mlongcalls -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -mdisable-hardware-atomics -Og -fno-shrink-wrap -fmacro-prefix-map=/home/<USER>/Documents/esp-idf-video-streaming-main=. -fmacro-prefix-map=/home/<USER>/esp/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -fzero-init-padding-bits=all -fno-malloc-dce -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj -MF esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj -c /home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c: In function 'usb_lib_handler_task':
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:194:17: error: implicit declaration of function 'usb_host_lib_handle_events' [-Wimplicit-function-declaration]
  194 |                 usb_host_lib_handle_events(portMAX_DELAY, &event_flags);
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:196:35: error: 'USB_HOST_LIB_EVENT_FLAGS_NO_CLIENTS' undeclared (first use in this function)
  196 |                 if (event_flags & USB_HOST_LIB_EVENT_FLAGS_NO_CLIENTS) {
      |                                   ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:196:35: note: each undeclared identifier is reported only once for each function it appears in
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:197:25: error: implicit declaration of function 'usb_host_device_free_all' [-Wimplicit-function-declaration]
  197 |                         usb_host_device_free_all();
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:201:35: error: 'USB_HOST_LIB_EVENT_FLAGS_ALL_FREE' undeclared (first use in this function)
  201 |                 if (event_flags & USB_HOST_LIB_EVENT_FLAGS_ALL_FREE) {
      |                                   ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c: In function 'initialize_usb_host_lib':
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:213:15: error: unknown type name 'usb_host_config_t'; did you mean 'esp_log_config_t'?
  213 |         const usb_host_config_t host_config = {
      |               ^~~~~~~~~~~~~~~~~
      |               esp_log_config_t
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:214:17: error: field name not in record or union initializer
  214 |                 .intr_flags = ESP_INTR_FLAG_LEVEL1
      |                 ^
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:214:17: note: (near initialization for 'host_config')
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:217:25: error: implicit declaration of function 'usb_host_install' [-Wimplicit-function-declaration]
  217 |         esp_err_t err = usb_host_install(&host_config);
      |                         ^~~~~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:224:17: error: implicit declaration of function 'usb_host_uninstall' [-Wimplicit-function-declaration]
  224 |                 usb_host_uninstall();
      |                 ^~~~~~~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c: At top level:
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:261:21: error: unknown type name 'uvc_frame_t'
  261 | void frame_callback(uvc_frame_t *frame, void *ptr)
      |                     ^~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:312:31: error: unknown type name 'libuvc_adapter_event_t'
  312 | static void libuvc_adapter_cb(libuvc_adapter_event_t event)
      |                               ^~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c: In function 'lvgl_task':
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:336:25: error: implicit declaration of function 'lv_timer_handler' [-Wimplicit-function-declaration]
  336 |                         lv_timer_handler();
      |                         ^~~~~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c: In function 'app_main':
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:390:11: warning: missing terminating " character
  390 |         */"
      |           ^
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:390:11: error: missing terminating " character
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c: At top level:
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:572:1: error: expected identifier or '(' before '}' token
  572 | }
      | ^
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:322:13: warning: 'camera_not_available' defined but not used [-Wunused-function]
  322 | static void camera_not_available(char *message) {
      |             ^~~~~~~~~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:317:20: warning: 'wait_for_event' defined but not used [-Wunused-function]
  317 | static EventBits_t wait_for_event(EventBits_t event)
      |                    ^~~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:247:13: warning: 'displayBuffer' defined but not used [-Wunused-function]
  247 | static void displayBuffer(uint8_t * buf, int buf_len, bool flag) {
      |             ^~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:237:13: warning: 'uninitialize_usb_host_lib' defined but not used [-Wunused-function]
  237 | static void uninitialize_usb_host_lib(void)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:209:18: warning: 'initialize_usb_host_lib' defined but not used [-Wunused-function]
  209 | static esp_err_t initialize_usb_host_lib(void)
      |                  ^~~~~~~~~~~~~~~~~~~~~~~
[13/22] Performing build step for 'bootloader'
[1/123] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/util.c.obj
[2/123] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/util.c.obj
[3/123] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj
[4/123] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj
[5/123] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_print.c.obj
[6/123] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj
[7/123] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/buffer/log_buffers.c.obj
[8/123] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log.c.obj
[9/123] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj
[10/123] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_format_text.c.obj
[11/123] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj
[12/123] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_serial_output.c.obj
[13/123] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj
[14/123] Building ASM object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj
[15/123] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj
[16/123] Building ASM object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj
[17/123] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj
[18/123] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj
[19/123] Linking C static library esp-idf/log/liblog.a
[20/123] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj
[21/123] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj
[22/123] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj
[23/123] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj
[24/123] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj
[25/123] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj
[26/123] Building C object esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj
[27/123] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj
[28/123] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj
[29/123] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj
[30/123] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj
[31/123] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj
[32/123] Linking C static library esp-idf/esp_rom/libesp_rom.a
[33/123] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj
[34/123] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj
[35/123] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj
[36/123] Linking C static library esp-idf/esp_common/libesp_common.a
[37/123] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj
[38/123] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj
[39/123] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj
[40/123] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj
[41/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj
[42/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj
[43/123] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj
[44/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj
[45/123] Linking C static library esp-idf/esp_hw_support/libesp_hw_support.a
[46/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj
[47/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj
[48/123] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj
[49/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj
[50/123] Linking C static library esp-idf/esp_system/libesp_system.a
[51/123] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj
[52/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj
[53/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj
[54/123] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj
[55/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_sha.c.obj
[56/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj
[57/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj
[58/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj
[59/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj
[60/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj
[61/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj
[62/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj
[63/123] Linking C static library esp-idf/efuse/libefuse.a
[64/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj
[65/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj
[66/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj
[67/123] Building C object esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj
[68/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj
[69/123] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj
[70/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj
[71/123] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj
[72/123] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj
[73/123] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj
[74/123] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj
[75/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj
[76/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj
[77/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj
[78/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj
[79/123] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj
[80/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj
[81/123] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj
[82/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj
[83/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj
[84/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj
[85/123] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj
[86/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj
[87/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj
[88/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj
[89/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj
[90/123] Linking C static library esp-idf/bootloader_support/libbootloader_support.a
[91/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj
[92/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj
[93/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj
[94/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj
[95/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj
[96/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj
[97/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj
[98/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj
[99/123] Linking C static library esp-idf/esp_bootloader_format/libesp_bootloader_format.a
[100/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj
[101/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj
[102/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj
[103/123] Linking C static library esp-idf/spi_flash/libspi_flash.a
[104/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj
[105/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj
[106/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj
[107/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj
[108/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/cam_periph.c.obj
[109/123] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/power_supply_periph.c.obj
[110/123] Generating project_elf_src_esp32s3.c
[111/123] Building C object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj
[112/123] Building C object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj
[113/123] Building C object CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj
[114/123] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj
[115/123] Linking C static library esp-idf/hal/libhal.a
[116/123] Building C object esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj
[117/123] Linking C static library esp-idf/micro-ecc/libmicro-ecc.a
[118/123] Linking C static library esp-idf/soc/libsoc.a
[119/123] Linking C static library esp-idf/xtensa/libxtensa.a
[120/123] Linking C static library esp-idf/main/libmain.a
[121/123] Linking C executable bootloader.elf
[122/123] Generating binary image from built executable
esptool.py v4.10.dev2
Creating esp32s3 image...
Merged 2 ELF sections
Successfully created esp32s3 image.
Generated /home/<USER>/Documents/esp-idf-video-streaming-main/build/bootloader/bootloader.bin
[123/123] cd /home/<USER>/Documents/esp-idf-video-streaming-main/build/bootloader && /home/<USER>/.espressif/python_env/idf6.0_py3.12_env/bin/python /home/<USER>/esp/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /home/<USER>/Documents/esp-idf-video-streaming-main/build/bootloader/bootloader.bin
Bootloader binary size 0x5320 bytes. 0x2ce0 bytes (35%) free.
ninja: build stopped: subcommand failed.
