Command: cmake -G Ninja -DPYTHON_DEPS_CHECKED=1 -DPYTHON=/home/<USER>/.espressif/python_env/idf6.0_py3.12_env/bin/python -DESP_PLATFORM=1 -DCCACHE_ENABLE=0 /home/<USER>/Documents/esp-idf-video-streaming-main
info: INFO: Symbol FRAME_RATE defined in multiple locations (see below). Please check if this is a correct behavior or a random name match:
    /home/<USER>/Documents/esp-idf-video-streaming-main/main/Kconfig.projbuild:82
    /home/<USER>/Documents/esp-idf-video-streaming-main/main/Kconfig.projbuild:73
warning: user value 68 on the int symbol LWIP_DHCP_OPTIONS_LEN (defined at /home/<USER>/esp/esp-idf/components/lwip/Kconfig:372) ignored due to being outside the active range ([69, 255]) -- falling back on defaults
