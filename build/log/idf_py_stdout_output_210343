Command: ninja all
[1/10] Performing build step for 'bootloader'
[1/1] cd /home/<USER>/Documents/esp-idf-video-streaming-main/build/bootloader && /home/<USER>/.espressif/python_env/idf6.0_py3.12_env/bin/python /home/<USER>/esp/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /home/<USER>/Documents/esp-idf-video-streaming-main/build/bootloader/bootloader.bin
Bootloader binary size 0x5320 bytes. 0x2ce0 bytes (35%) free.
[2/10] No install step for 'bootloader'
[3/10] Completed 'bootloader'
[4/10] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj 
/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-15.1.0_20250607/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc -DESP_MDNS_VERSION_NUMBER=\"1.8.2\" -DESP_PLATFORM -DIDF_VER=\"v6.0-dev-1833-g758939caec\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -DUNITY_INCLUDE_CONFIG_H -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -I/home/<USER>/Documents/esp-idf-video-streaming-main/build/config -I/home/<USER>/Documents/esp-idf-video-streaming-main/main -I/home/<USER>/esp/esp-idf/components/newlib/platform_include -I/home/<USER>/esp/esp-idf/components/freertos/config/include -I/home/<USER>/esp/esp-idf/components/freertos/config/include/freertos -I/home/<USER>/esp/esp-idf/components/freertos/config/xtensa/include -I/home/<USER>/esp/esp-idf/components/freertos/FreeRTOS-Kernel/include -I/home/<USER>/esp/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -I/home/<USER>/esp/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -I/home/<USER>/esp/esp-idf/components/freertos/esp_additions/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/include/soc -I/home/<USER>/esp/esp-idf/components/esp_hw_support/dma/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/ldo/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/debug_probe/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/power_supply/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/include/soc/esp32s3 -I/home/<USER>/esp/esp-idf/components/esp_hw_support/port/esp32s3/. -I/home/<USER>/esp/esp-idf/components/esp_hw_support/port/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_hw_support/mspi_timing_tuning/port/esp32s3/. -I/home/<USER>/esp/esp-idf/components/esp_hw_support/mspi_timing_tuning/port/esp32s3/include -I/home/<USER>/esp/esp-idf/components/heap/include -I/home/<USER>/esp/esp-idf/components/heap/tlsf -I/home/<USER>/esp/esp-idf/components/log/include -I/home/<USER>/esp/esp-idf/components/soc/include -I/home/<USER>/esp/esp-idf/components/soc/esp32s3 -I/home/<USER>/esp/esp-idf/components/soc/esp32s3/include -I/home/<USER>/esp/esp-idf/components/soc/esp32s3/register -I/home/<USER>/esp/esp-idf/components/hal/platform_port/include -I/home/<USER>/esp/esp-idf/components/hal/esp32s3/include -I/home/<USER>/esp/esp-idf/components/hal/include -I/home/<USER>/esp/esp-idf/components/esp_rom/include -I/home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -I/home/<USER>/esp/esp-idf/components/esp_rom/esp32s3 -I/home/<USER>/esp/esp-idf/components/esp_common/include -I/home/<USER>/esp/esp-idf/components/esp_system/include -I/home/<USER>/esp/esp-idf/components/esp_system/port/soc -I/home/<USER>/esp/esp-idf/components/esp_system/port/include/private -I/home/<USER>/esp/esp-idf/components/xtensa/esp32s3/include -I/home/<USER>/esp/esp-idf/components/xtensa/include -I/home/<USER>/esp/esp-idf/components/xtensa/deprecated_include -I/home/<USER>/esp/esp-idf/components/lwip/include -I/home/<USER>/esp/esp-idf/components/lwip/include/apps -I/home/<USER>/esp/esp-idf/components/lwip/include/apps/sntp -I/home/<USER>/esp/esp-idf/components/lwip/lwip/src/include -I/home/<USER>/esp/esp-idf/components/lwip/port/include -I/home/<USER>/esp/esp-idf/components/lwip/port/freertos/include -I/home/<USER>/esp/esp-idf/components/lwip/port/esp32xx/include -I/home/<USER>/esp/esp-idf/components/lwip/port/esp32xx/include/arch -I/home/<USER>/esp/esp-idf/components/lwip/port/esp32xx/include/sys -I/home/<USER>/Documents/esp-idf-video-streaming-main/managed_components/espressif__mdns/include -I/home/<USER>/esp/esp-idf/components/console -I/home/<USER>/esp/esp-idf/components/vfs/include -I/home/<USER>/esp/esp-idf/components/esp_vfs_console/include -I/home/<USER>/esp/esp-idf/components/esp_netif/include -I/home/<USER>/esp/esp-idf/components/esp_event/include -I/home/<USER>/esp/esp-idf/components/esp_app_format/include -I/home/<USER>/esp/esp-idf/components/esp_bootloader_format/include -I/home/<USER>/esp/esp-idf/components/app_update/include -I/home/<USER>/esp/esp-idf/components/bootloader_support/include -I/home/<USER>/esp/esp-idf/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp/esp-idf/components/esp_partition/include -I/home/<USER>/esp/esp-idf/components/efuse/include -I/home/<USER>/esp/esp-idf/components/efuse/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_security/include -I/home/<USER>/esp/esp-idf/components/esp_driver_gpio/include -I/home/<USER>/esp/esp-idf/components/esp_pm/include -I/home/<USER>/esp/esp-idf/components/mbedtls/port/include -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/include -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/library -I/home/<USER>/esp/esp-idf/components/mbedtls/esp_crt_bundle/include -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/3rdparty/everest/include -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/3rdparty/p256-m -I/home/<USER>/esp/esp-idf/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -I/home/<USER>/esp/esp-idf/components/esp_mm/include -I/home/<USER>/esp/esp-idf/components/spi_flash/include -I/home/<USER>/esp/esp-idf/components/pthread/include -I/home/<USER>/esp/esp-idf/components/esp_timer/include -I/home/<USER>/esp/esp-idf/components/esp_driver_gptimer/include -I/home/<USER>/esp/esp-idf/components/esp_ringbuf/include -I/home/<USER>/esp/esp-idf/components/esp_driver_uart/include -I/home/<USER>/esp/esp-idf/components/app_trace/include -I/home/<USER>/esp/esp-idf/components/nvs_flash/include -I/home/<USER>/esp/esp-idf/components/esp_phy/include -I/home/<USER>/esp/esp-idf/components/esp_phy/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_driver_usb_serial_jtag/include -I/home/<USER>/esp/esp-idf/components/wpa_supplicant/include -I/home/<USER>/esp/esp-idf/components/wpa_supplicant/port/include -I/home/<USER>/esp/esp-idf/components/wpa_supplicant/esp_supplicant/include -I/home/<USER>/esp/esp-idf/components/esp_coex/include -I/home/<USER>/esp/esp-idf/components/esp_wifi/include -I/home/<USER>/esp/esp-idf/components/esp_wifi/include/local -I/home/<USER>/esp/esp-idf/components/esp_wifi/wifi_apps/include -I/home/<USER>/esp/esp-idf/components/esp_wifi/wifi_apps/nan_app/include -I/home/<USER>/esp/esp-idf/components/esp_driver_spi/include -I/home/<USER>/esp/esp-idf/components/esp_gdbstub/include -I/home/<USER>/esp/esp-idf/components/unity/include -I/home/<USER>/esp/esp-idf/components/unity/unity/src -I/home/<USER>/esp/esp-idf/components/cmock/CMock/src -I/home/<USER>/esp/esp-idf/components/esp_driver_pcnt/include -I/home/<USER>/esp/esp-idf/components/esp_driver_mcpwm/include -I/home/<USER>/esp/esp-idf/components/esp_driver_ana_cmpr/include -I/home/<USER>/esp/esp-idf/components/esp_driver_i2s/include -I/home/<USER>/esp/esp-idf/components/sdmmc/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sd_intf/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdmmc/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdmmc/legacy/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdspi/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdio/include -I/home/<USER>/esp/esp-idf/components/esp_driver_dac/include -I/home/<USER>/esp/esp-idf/components/esp_driver_bitscrambler/include -I/home/<USER>/esp/esp-idf/components/esp_driver_rmt/include -I/home/<USER>/esp/esp-idf/components/esp_driver_tsens/include -I/home/<USER>/esp/esp-idf/components/esp_driver_sdm/include -I/home/<USER>/esp/esp-idf/components/esp_driver_i2c/include -I/home/<USER>/esp/esp-idf/components/esp_driver_ledc/include -I/home/<USER>/esp/esp-idf/components/esp_driver_parlio/include -I/home/<USER>/esp/esp-idf/components/esp_driver_twai/include -I/home/<USER>/esp/esp-idf/components/driver/deprecated -I/home/<USER>/esp/esp-idf/components/driver/i2c/include -I/home/<USER>/esp/esp-idf/components/driver/touch_sensor/include -I/home/<USER>/esp/esp-idf/components/driver/twai/include -I/home/<USER>/esp/esp-idf/components/driver/touch_sensor/esp32s3/include -I/home/<USER>/esp/esp-idf/components/http_parser -I/home/<USER>/esp/esp-idf/components/esp-tls -I/home/<USER>/esp/esp-idf/components/esp-tls/esp-tls-crypto -I/home/<USER>/esp/esp-idf/components/esp_adc/include -I/home/<USER>/esp/esp-idf/components/esp_adc/interface -I/home/<USER>/esp/esp-idf/components/esp_adc/esp32s3/include -I/home/<USER>/esp/esp-idf/components/esp_driver_isp/include -I/home/<USER>/esp/esp-idf/components/esp_driver_cam/include -I/home/<USER>/esp/esp-idf/components/esp_driver_cam/interface -I/home/<USER>/esp/esp-idf/components/esp_driver_cam/dvp/include -I/home/<USER>/esp/esp-idf/components/esp_driver_i3c/include -I/home/<USER>/esp/esp-idf/components/esp_psram/include -I/home/<USER>/esp/esp-idf/components/esp_psram/xip_impl/include -I/home/<USER>/esp/esp-idf/components/esp_driver_jpeg/include -I/home/<USER>/esp/esp-idf/components/esp_driver_ppa/include -I/home/<USER>/esp/esp-idf/components/esp_driver_touch_sens/include -I/home/<USER>/esp/esp-idf/components/esp_driver_touch_sens/hw_ver2/include -I/home/<USER>/esp/esp-idf/components/esp_eth/include -I/home/<USER>/esp/esp-idf/components/esp_hid/include -I/home/<USER>/esp/esp-idf/components/tcp_transport/include -I/home/<USER>/esp/esp-idf/components/esp_http_client/include -I/home/<USER>/esp/esp-idf/components/esp_http_server/include -I/home/<USER>/esp/esp-idf/components/esp_https_ota/include -I/home/<USER>/esp/esp-idf/components/esp_https_server/include -I/home/<USER>/esp/esp-idf/components/esp_lcd/include -I/home/<USER>/esp/esp-idf/components/esp_lcd/interface -I/home/<USER>/esp/esp-idf/components/esp_lcd/rgb/include -I/home/<USER>/esp/esp-idf/components/protobuf-c/protobuf-c -I/home/<USER>/esp/esp-idf/components/protocomm/include/common -I/home/<USER>/esp/esp-idf/components/protocomm/include/security -I/home/<USER>/esp/esp-idf/components/protocomm/include/transports -I/home/<USER>/esp/esp-idf/components/protocomm/include/crypto/srp6a -I/home/<USER>/esp/esp-idf/components/protocomm/proto-c -I/home/<USER>/esp/esp-idf/components/esp_local_ctrl/include -I/home/<USER>/esp/esp-idf/components/espcoredump/include -I/home/<USER>/esp/esp-idf/components/espcoredump/include/port/xtensa -I/home/<USER>/esp/esp-idf/components/wear_levelling/include -I/home/<USER>/esp/esp-idf/components/fatfs/diskio -I/home/<USER>/esp/esp-idf/components/fatfs/src -I/home/<USER>/esp/esp-idf/components/fatfs/vfs -I/home/<USER>/esp/esp-idf/components/idf_test/include -I/home/<USER>/esp/esp-idf/components/idf_test/include/esp32s3 -I/home/<USER>/esp/esp-idf/components/ieee802154/include -I/home/<USER>/esp/esp-idf/components/json/cJSON -I/home/<USER>/esp/esp-idf/components/mqtt/esp-mqtt/include -I/home/<USER>/esp/esp-idf/components/nvs_sec_provider/include -I/home/<USER>/esp/esp-idf/components/perfmon/include -I/home/<USER>/esp/esp-idf/components/rt/include -I/home/<USER>/esp/esp-idf/components/spiffs/include -I/home/<USER>/esp/esp-idf/components/touch_element/include -I/home/<USER>/esp/esp-idf/components/usb/include -I/home/<USER>/esp/esp-idf/components/wifi_provisioning/include -I/home/<USER>/Documents/esp-idf-video-streaming-main/components/conversions/include -I/home/<USER>/Documents/esp-idf-video-streaming-main/components/conversions/private_include -mlongcalls -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -mdisable-hardware-atomics -Og -fno-shrink-wrap -fmacro-prefix-map=/home/<USER>/Documents/esp-idf-video-streaming-main=. -fmacro-prefix-map=/home/<USER>/esp/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -fzero-init-padding-bits=all -fno-malloc-dce -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj -MF esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj -c /home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:587:1: error: expected identifier or '(' before '}' token
  587 | }
      | ^
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:253:13: warning: 'displayBuffer' defined but not used [-Wunused-function]
  253 | static void displayBuffer(uint8_t * buf, int buf_len, bool flag) {
      |             ^~~~~~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:59:27: warning: 'app_flags' defined but not used [-Wunused-variable]
   59 | static EventGroupHandle_t app_flags;
      |                           ^~~~~~~~~
/home/<USER>/Documents/esp-idf-video-streaming-main/main/main.c:58:26: warning: 'ready_to_uninstall_usb' defined but not used [-Wunused-variable]
   58 | static SemaphoreHandle_t ready_to_uninstall_usb;
      |                          ^~~~~~~~~~~~~~~~~~~~~~
ninja: build stopped: subcommand failed.
